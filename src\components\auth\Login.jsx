import { EyeIcon, EyeOffIcon } from 'lucide-react';
import { useState } from 'react';
import { useAuthStore } from '../../stores/authStore';
import AnimationBox from '../reuseable/AnimationBox';
import Header from '../reuseable/Header';
import './Login.css';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const login = useAuthStore((state) => state.login);

  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }

    const success = login(email, password);
    if (!success) {
      setError('Invalid credentials. Please try again.');
    }
  };

  return (
    <div className="login-container">

      <Header />
      <AnimationBox className="login-box">

        <h4 className='h4'>Sign In</h4>

        <div className='social-login'>
          <button className='social-btn google body2 '>Continue with Google<img src='/icons/google.svg' alt='google' /></button>
          <button className='social-btn apple body2'>Continue with Apple<img src='/icons/apple.svg' alt='google' /></button>
          <button className='social-btn microsoft body2'>Continue with Microsoft<img src='/icons/microsoft.svg' alt='google' /></button>
          <button className='social-btn facebook body2'>Continue with Facebook<img src='/icons/facebook.svg' alt='google' /></button>
        </div>

        <div className="divider"><span>OR</span></div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email:</label>
            <input
              type="text"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address..."
              required
            />
            {error.includes('email') && <div className="error-message">{error}</div>}
          </div>
          <div className="form-group">
            <label htmlFor="password">Password:</label>
            <div className="password-wrapper">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password..."
                required
              />
              <button
                type="button"
                className="toggle-password"
                onClick={() => setShowPassword((prev) => !prev)}
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <EyeIcon size={20} /> : <EyeOffIcon size={20} />}
              </button>
            </div>
            {error && !error.includes('email') && (
              <div className="error-message">{error}</div>)}
          </div>


          <button type="submit" className="body3-bold login-button">Sign in</button>
        </form>
        <div className='body4 forgot-password'><a href="/forgot-password">Forgot password?</a></div>

        <hr className="hr" />
        <div className="signup">
          <span className='body2'>New to the platform?</span>
          <button className='body3-bold'>Sign up</button>
        </div>
      </AnimationBox>
    </div>
  );
};

export default Login;