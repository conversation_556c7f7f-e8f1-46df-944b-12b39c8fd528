import React, { useEffect, useRef } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import "./App.css";
import Login from "./components/auth/Login";
import Dashboard from "./components/Dashboard";
import ErrorBoundary from "./components/ErrorBoundary";
import { useAudioStore } from "./stores/audioStore";
import { useAuthStore } from "./stores/authStore";

import { ToastContainer } from "./components/Toast";
import { useErrorHandler } from "./hooks/useErrorHandler";
import { useLocalStorage } from "./hooks/useLocalStorage";
import { STORAGE_KEYS, TEXT_SIZES } from "./utils/constants";

// Simple course structure for sidebar
const courseData = {
  id: "1",
  title: "KEPH 102 - Physics Course",
  topics: [
    {
      id: "keph102",
      title: "KEPH 102",
      pdfUrl: "/keph102.pdf",
      audioSources: ["/audio1.mp3", "/audio2.mp3", "/audio3.mp3", "/audio4.mp3"],
      content: {
        heading: "KEPH 102 - Physics Course Material",
        body: [
          {
            id: "content-1",
            type: "pdf",
            pdfUrl: "/keph102.pdf",
          },
        ],
      },
    },
  ],
};

// PDF data for MainContent
const pdfData = courseData.topics[0];

function App() {
  const { errors, addError, removeError } = useErrorHandler();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  // Initialize audio context on first user interaction
  useEffect(() => {
    const initializeAudioContext = () => {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      if (AudioContext) {
        const audioContext = new AudioContext();

        // Resume audio context if suspended
        if (audioContext.state === "suspended") {
          audioContext.resume();
        }
      }
    };

    // Add event listener for first user interaction
    const handleFirstInteraction = () => {
      initializeAudioContext();
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };

    document.addEventListener("click", handleFirstInteraction);
    document.addEventListener("touchstart", handleFirstInteraction);

    return () => {
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };
  }, []);

  return (
    <Router>
      <ErrorBoundary
        message="Something went wrong with the application. Please refresh the page."
        onError={(error, errorInfo) => {
          console.error("App Error Boundary:", error, errorInfo);
          addError({
            id: Date.now(),
            message: "An unexpected error occurred. Please refresh the page.",
            type: "error",
          });
        }}
      >
        <Routes>
          <Route
            path="/login"
            element={!isAuthenticated ? <Login /> : <Navigate to="/dashboard" replace />}
          />
          <Route
            path="/dashboard"
            element={isAuthenticated ? <Dashboard courseData={courseData} pdfData={pdfData} /> : <Navigate to="/login" replace />}
          />
          <Route
            path="/"
            element={<Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />}
          />
        </Routes>
        <ToastContainer
          position="bottom-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
      </ErrorBoundary>
    </Router>
  );
}

export default App;
