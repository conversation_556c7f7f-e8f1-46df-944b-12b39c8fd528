import React, { useEffect, useRef } from "react";
import "./App.css";
import Login from "./components/auth/Login";
import CourseStructure from "./components/CourseStructure";
import ErrorBoundary from "./components/ErrorBoundary";
import MainContent from "./components/MainContent";
import ToolsPanel from "./components/ToolsPanel";
import { useAudioStore } from "./stores/audioStore";
import { useAuthStore } from "./stores/authStore";

import { ToastContainer } from "./components/Toast";
import { useErrorHandler } from "./hooks/useErrorHandler";
import { useLocalStorage } from "./hooks/useLocalStorage";
import { STORAGE_KEYS, TEXT_SIZES } from "./utils/constants";

// Simple course structure for sidebar
const courseData = {
  id: "1",
  title: "KEPH 102 - Physics Course",
  topics: [
    {
      id: "keph102",
      title: "KEPH 102",
      pdfUrl: "/keph102.pdf",
      audioSources: ["/audio1.mp3", "/audio2.mp3", "/audio3.mp3", "/audio4.mp3"],
      content: {
        heading: "KEPH 102 - Physics Course Material",
        body: [
          {
            id: "content-1",
            type: "pdf",
            pdfUrl: "/keph102.pdf",
          },
        ],
      },
    },
  ],
};

// PDF data for MainContent
const pdfData = courseData.topics[0];

function App() {
  const audioRef = useRef(null);
  const [textSize, setTextSize] = useLocalStorage(STORAGE_KEYS.TEXT_SIZE, TEXT_SIZES.NORMAL);
  const [activeTopicId, setActiveTopicId] = React.useState("keph102");

  const { errors, addError, removeError } = useErrorHandler();
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  const initialize = useAudioStore((state) => state.initialize);
  const loadPagePlaylist = useAudioStore((state) => state.loadPagePlaylist);

  // Initialize audio context on first user interaction
  useEffect(() => {
    const initializeAudioContext = () => {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      if (AudioContext) {
        const audioContext = new AudioContext();

        // Resume audio context if suspended
        if (audioContext.state === "suspended") {
          audioContext.resume();
        }
      }
    };

    // Add event listener for first user interaction
    const handleFirstInteraction = () => {
      initializeAudioContext();
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };

    document.addEventListener("click", handleFirstInteraction);
    document.addEventListener("touchstart", handleFirstInteraction);

    return () => {
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };
  }, []);

  // Initialize audio store
  useEffect(() => {
    if (audioRef.current) {
      const cleanup = initialize(audioRef, pdfData.audioSources);
      return cleanup;
    }
  }, [initialize]);

  // Load audio playlist for the PDF
  useEffect(() => {
    loadPagePlaylist(pdfData.id, pdfData.audioSources);
  }, [loadPagePlaylist]);

  if (!isAuthenticated) {
    return <Login />;
  }

  return (
    <ErrorBoundary
      message="Something went wrong with the application. Please refresh the page."
      onError={(error, errorInfo) => {
        console.error("App Error Boundary:", error, errorInfo);
        addError({
          id: Date.now(),
          message: "An unexpected error occurred. Please refresh the page.",
          type: "error",
        });
      }}
    >
      <div className="app-container">
        <audio ref={audioRef} style={{ display: "none" }} />
        <div className="course-structure-sidebar">
          <CourseStructure
            topics={courseData.topics}
            activeTopicId={activeTopicId}
            onSelectTopic={setActiveTopicId}
            courseData={courseData}
          />
        </div>
        <div className="main-content-panel">
          <MainContent
            topic={pdfData}
            textSize={textSize}
            onError={addError}
          />
        </div>
        <div className="tools-panel-sidebar">
          <ToolsPanel
            textSize={textSize}
            onTextSizeChange={setTextSize}
          />
        </div>
        <ToastContainer
          position="bottom-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
      </div>
    </ErrorBoundary>
  );
}

export default App;
