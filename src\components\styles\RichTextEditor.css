Rich Text Editor Container .rich-text-editor-container {
  width: 100%;
  border: 1px solid #e5e2d9;
  border-radius: 6px;
  background-color: #fffbf3;
  box-sizing: border-box;
  overflow: hidden;
}

.rich-text-editor-content {
  width: 100%;
  min-height: 280px !important; /* Updated minimum height */
  padding: 12px 16px; /* Better padding for alignment */
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  outline: none;
  border: none;
  background: transparent;
  resize: vertical;
  box-sizing: border-box;
  position: relative; /* For proper placeholder positioning */
}

/* Toolbar Styling */
.rich-text-toolbar {
  display: flex;
  gap: 8px;
  align-items: center;
  padding-top: 12px;
  /* background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%); */
  background-color: #fffbf3;
  /* border-bottom: 1px solid #e5e2d9; */
  flex-wrap: wrap;
  /* box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); */
}

/* Toolbar <PERSON><PERSON> Styling - Enhanced design */
.rich-text-btn {
  padding: 8px 12px !important;
  border: 1px solid #e5e2d9 !important;
  border-radius: 6px !important;
  /* background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%) !important; */
  color: #374151 !important;
  cursor: pointer;
  font-size: 14px !important;
  font-family: "Montserrat", sans-serif;
  font-weight: 600 !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
  box-sizing: border-box;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
}

.rich-text-btn:hover:not(.active) {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%) !important;
  border-color: #9ca3af !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.rich-text-btn.active {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%) !important;
  color: #ffffff !important;
  border-color: #1f2937 !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  transform: translateY(1px);
}

.rich-text-btn.active:hover {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%) !important;
  color: #ffffff !important;
  border-color: #374151 !important;
  transform: translateY(0px);
}

/* Add a subtle glow effect for active buttons */
.rich-text-btn.active::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 8px;
  z-index: -1;
  opacity: 0.3;
  filter: blur(4px);
}

/* Content Formatting Styles */
.rich-text-editor-content strong,
.rich-text-editor-content b {
  font-weight: bold;
}

.rich-text-editor-content em,
.rich-text-editor-content i {
  font-style: italic;
}

.rich-text-editor-content u {
  text-decoration: underline;
}

.rich-text-editor-content ul,
.slate-bulleted-list {
  list-style: none;
  padding-left: 0;
  margin: 0.75em 0;
}

.rich-text-editor-content ol {
  list-style: decimal;
  padding-left: 2em;
  margin: 0.75em 0;
}

.rich-text-editor-content li,
.slate-list-item {
  margin-bottom: 0.5em;
  padding-left: 1.5em;
  position: relative;
  line-height: 1.6;
}

.slate-list-item::before {
  content: "•";
  color: #6b7280;
  font-weight: bold;
  position: absolute;
  left: 0.5em;
  top: 0;
}

.slate-paragraph {
  margin: 0.5em 0;
  line-height: 1.6;
  /* Ensure paragraphs maintain proper spacing and alignment */
  min-height: 1.6em; /* Prevents empty paragraphs from collapsing */
}

.rich-text-editor-content p {
  margin: 0.5em 0;
  line-height: 1.6;
  min-height: 1.6em;
}

.rich-text-editor-content p:first-child {
  margin-top: 0;
}

.rich-text-editor-content p:last-child {
  margin-bottom: 0;
}

/* Fixed Placeholder styling - Better alignment with cursor */
.rich-text-editor-content[data-slate-placeholder]:not(
    [data-slate-string=""]
  ):empty::before {
  content: attr(data-slate-placeholder);
  color: #999;
  font-style: italic;
  position: absolute;
  top: 12px; /* Match the padding-top of the content area */
  left: 16px; /* Match the padding-left of the content area */
  pointer-events: none;
  font-size: 14px;
  line-height: 1.5;
  z-index: 1;
}

/* Alternative placeholder approach for better compatibility */
.rich-text-editor-content[data-slate-placeholder]:empty
  > [data-slate-node="element"]:first-child
  > [data-slate-node="text"]:first-child:empty::before {
  content: attr(data-slate-placeholder);
  color: #999;
  font-style: italic;
  pointer-events: none;
  position: absolute;
}

/* Ensure proper cursor positioning */
.rich-text-editor-content > [data-slate-node="element"] {
  position: relative;
}

/* Focus state for the entire editor */
.rich-text-editor-container:focus-within {
  border-color: #333;
}

/* Selection styling */
.rich-text-editor-content ::selection {
  background-color: rgba(51, 51, 51, 0.2);
}

/* Additional styling for better UX */
.rich-text-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f5f5f5 !important;
  color: #999 !important;
}

/* Improve button spacing and alignment */
.rich-text-toolbar .rich-text-btn + .rich-text-btn {
  margin-left: 2px;
}

/* Ensure consistent button appearance */
.rich-text-btn svg {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

/* Better focus states */
.rich-text-btn:focus {
  outline: 2px solid #333;
  outline-offset: 2px;
}

.toolbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rich-text-toolbar {
    padding: 6px 8px;
  }

  .rich-text-btn {
    min-width: 28px !important;
    height: 28px !important;
    padding: 4px 8px !important;
    font-size: 12px !important;
  }

  .rich-text-editor-content {
    min-height: 120px; /* Smaller minimum height on mobile */
    padding: 10px 12px;
  }

  /* Adjust placeholder position for mobile */
  .rich-text-editor-content[data-slate-placeholder]:not(
      [data-slate-string=""]
    ):empty::before {
    top: 10px;
    left: 12px;
  }
}
