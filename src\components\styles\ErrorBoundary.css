.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
  background-color: #fffbf3;
  font-family: "Montserrat", sans-serif;
}

.error-boundary-content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.error-boundary h2 {
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
  font-size: 24px;
  color: #333;
  margin-bottom: 16px;
}

.error-boundary p {
  color: #666;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 24px;
}

.error-details {
  text-align: left;
  margin: 20px 0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.error-details summary {
  padding: 12px 16px;
  background-color: #f8f9fa;
  cursor: pointer;
  font-weight: 500;
  border-bottom: 1px solid #e0e0e0;
}

.error-details summary:hover {
  background-color: #f1f3f4;
}

.error-stack {
  padding: 16px;
  margin: 0;
  background-color: #f8f9fa;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #d73a49;
  white-space: pre-wrap;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-button,
.reload-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-family: "Montserrat", sans-serif;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.retry-button {
  background-color: #333;
  color: white;
}

.retry-button:hover {
  background-color: #555;
  transform: translateY(-1px);
}

.reload-button {
  background-color: transparent;
  color: #333;
  border: 1px solid #ccc;
}

.reload-button:hover {
  background-color: #f5f2eb;
  border-color: #bbb;
  transform: translateY(-1px);
}

.retry-button:active,
.reload-button:active {
  transform: translateY(0);
}

/* Responsive design */
@media (max-width: 768px) {
  .error-boundary {
    padding: 16px;
    min-height: 300px;
  }

  .error-boundary-content {
    padding: 24px;
  }

  .error-boundary h2 {
    font-size: 20px;
  }

  .error-boundary p {
    font-size: 14px;
  }

  .error-actions {
    flex-direction: column;
  }

  .retry-button,
  .reload-button {
    width: 100%;
  }
}
