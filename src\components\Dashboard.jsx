import React, { useEffect, useRef } from "react";
import CourseStructure from "./CourseStructure";
import MainContent from "./MainContent";
import ToolsPanel from "./ToolsPanel";
import { useAudioStore } from "../stores/audioStore";
import { useLocalStorage } from "../hooks/useLocalStorage";
import { STORAGE_KEYS, TEXT_SIZES } from "../utils/constants";

const Dashboard = ({ courseData, pdfData }) => {
  const audioRef = useRef(null);
  const [textSize, setTextSize] = useLocalStorage(STORAGE_KEYS.TEXT_SIZE, TEXT_SIZES.NORMAL);
  const [activeTopicId, setActiveTopicId] = React.useState("keph102");

  const initialize = useAudioStore((state) => state.initialize);
  const loadPagePlaylist = useAudioStore((state) => state.loadPagePlaylist);

  // Handle first user interaction for audio
  useEffect(() => {
    let hasInteracted = false;

    const handleFirstInteraction = () => {
      if (!hasInteracted && audioRef.current) {
        hasInteracted = true;
        // Initialize audio context on first user interaction
        audioRef.current.load();
        document.removeEventListener("click", handleFirstInteraction);
        document.removeEventListener("touchstart", handleFirstInteraction);
      }
    };

    document.addEventListener("click", handleFirstInteraction);
    document.addEventListener("touchstart", handleFirstInteraction);

    return () => {
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };
  }, []);

  // Initialize audio store
  useEffect(() => {
    if (audioRef.current) {
      const cleanup = initialize(audioRef, pdfData.audioSources);
      return cleanup;
    }
  }, [initialize]);

  // Load audio playlist for the PDF
  useEffect(() => {
    loadPagePlaylist(pdfData.id, pdfData.audioSources);
  }, [loadPagePlaylist]);

  return (
    <div className="app-container">
      <audio ref={audioRef} style={{ display: "none" }} />
      
      {/* Course Structure Panel (Left Side) */}
      <div className="course-structure-sidebar">
        <CourseStructure
          topics={courseData.topics}
          activeTopicId={activeTopicId}
          onSelectTopic={setActiveTopicId}
          courseData={courseData}
        />
      </div>
      
      {/* Main Content Area (Center) */}
      <div className="main-content-panel">
        <MainContent
          topic={pdfData}
          textSize={textSize}
        />
      </div>
      
      {/* Tools Panel (Right Side) */}
      <div className="tools-panel-sidebar">
        <ToolsPanel
          textSize={textSize}
          onTextSizeChange={setTextSize}
        />
      </div>
    </div>
  );
};

export default Dashboard;
