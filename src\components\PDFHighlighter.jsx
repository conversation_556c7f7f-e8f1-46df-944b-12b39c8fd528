import React, { useCallback, useEffect, useMemo } from 'react';
import {
  AreaHighlight,
  Highlight,
  PdfHighlighter,
  PdfLoader,
  Popup,
  Tip,
} from 'react-pdf-highlighter';
import 'react-pdf-highlighter/dist/style.css';
import { useAuthStore } from '../stores/authStore';
import { useColorStore } from '../stores/colorStore';
import { useHighlightStore } from '../stores/highlightStore';
import './styles/PDFHighlighter.css';

const getNextId = () => String(Math.random()).slice(2);

const HighlightPopup = ({ comment }) => {
  return comment.text ? (
    <div className="highlight-popup">
      <div className="highlight-comment">
        {comment.emoji && <span className="comment-emoji">{comment.emoji}</span>}
        <span className="comment-text">{comment.text}</span>
      </div>
    </div>
  ) : null;
};

const PDFHighlighter = ({ pdfUrl, pageId, topicId, textSize }) => {
  const { selectedColor } = useColorStore();
  const { currentUser, isAuthenticated } = useAuthStore();
  const {
    isHighlightMode,
    getHighlights,
    addHighlight,
    updateHighlight,
    removeHighlight,
    _hasHydrated,
    setHasHydrated
  } = useHighlightStore();

  // Ensure hydration is complete
  useEffect(() => {
    if (!_hasHydrated) {
      // Force hydration if not already done
      const timeoutId = setTimeout(() => {
        setHasHydrated(true);
      }, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [_hasHydrated, setHasHydrated]);

  // Get highlights for current page/topic (user-specific)
  const highlights = (_hasHydrated && isAuthenticated && currentUser)
    ? getHighlights(pageId, topicId)
    : [];

  // Debug logging
  useEffect(() => {
    console.log('PDFHighlighter Debug:', {
      _hasHydrated,
      isAuthenticated,
      currentUser: currentUser?.email,
      pageId,
      topicId,
      highlightsCount: highlights.length,
      isHighlightMode
    });
  }, [_hasHydrated, isAuthenticated, currentUser, pageId, topicId, highlights.length, isHighlightMode]);

  // Handle adding new highlights with vector position data
  const handleAddHighlight = useCallback((highlight) => {
    if (!isAuthenticated || !currentUser) {
      console.warn('User must be authenticated to add highlights');
      return;
    }

    console.log(`Adding highlight for user ${currentUser.username}:`, highlight);

    const newHighlight = {
      ...highlight,
      id: getNextId(),
      createdAt: new Date().toISOString(),
      content: highlight.content, // Preserve the selected text content
      position: {
        boundingRect: {
          x1: highlight.position.boundingRect.x1,
          y1: highlight.position.boundingRect.y1,
          x2: highlight.position.boundingRect.x2,
          y2: highlight.position.boundingRect.y2,
          width: highlight.position.boundingRect.width || 0,
          height: highlight.position.boundingRect.height || 0,
          pageNumber: highlight.position.boundingRect.pageNumber
        },
        rects: highlight.position.rects.map(rect => ({
          x1: rect.x1,
          y1: rect.y1,
          x2: rect.x2,
          y2: rect.y2,
          width: rect.width || 0,
          height: rect.height || 0,
          pageNumber: rect.pageNumber
        })),
        pageNumber: highlight.position.pageNumber
      },
      comment: {
        text: '',
        emoji: '💡',
        color: selectedColor?.id || 'yellow',
        backgroundColor: selectedColor?.backgroundColor || '#ffeb3b',
      }
    };

    addHighlight(pageId, topicId, newHighlight);
  }, [addHighlight, pageId, topicId, selectedColor, isAuthenticated, currentUser]);

  // Handle updating highlights
  const handleUpdateHighlight = useCallback((highlightId, position, content) => {
    console.log('Updating highlight:', highlightId, position, content);
    updateHighlight(pageId, topicId, highlightId, position, content);
  }, [updateHighlight, pageId, topicId]);

  // Handle removing highlights
  const handleRemoveHighlight = useCallback((highlightId) => {
    console.log('Removing highlight:', highlightId);
    removeHighlight(pageId, topicId, highlightId);
  }, [removeHighlight, pageId, topicId]);

  // Scale factor based on text size
  const scaleFactor = useMemo(() => {
    switch (textSize) {
      case 'small': return 0.8;
      case 'large': return 1.2;
      default: return 1.0;
    }
  }, [textSize]);

  if (!pdfUrl) {
    return (
      <div className="pdf-error">
        <p>No PDF URL provided</p>
      </div>
    );
  }

  // Check if user is authenticated
  if (!isAuthenticated || !currentUser) {
    return (
      <div className="pdf-error">
        <p>Please log in to view and create highlights</p>
      </div>
    );
  }

  // Show loading state until hydration is complete
  if (!_hasHydrated) {
    return (
      <div className="pdf-loading">
        <p>Loading highlights...</p>
      </div>
    );
  }

  return (
    <div className={`pdf-highlighter-container ${textSize}`}>
      <PdfLoader url={pdfUrl} beforeLoad={<div className="pdf-loading">Loading PDF...</div>}>
        {(pdfDocument) => (
          <PdfHighlighter
            pdfDocument={pdfDocument}
            enableAreaSelection={(event) => event.altKey}
            onScrollChange={() => { }}
            scrollRef={() => { }}
            onSelectionFinished={(
              position,
              content,
              hideTipAndSelection,
              transformSelection
            ) => (
              <Tip
                onOpen={transformSelection}
                onConfirm={(comment) => {
                  handleAddHighlight({
                    content,
                    position,
                    comment: {
                      text: comment.text || '',
                      emoji: comment.emoji || '💡',
                      color: selectedColor?.id || 'yellow',
                      backgroundColor: selectedColor?.backgroundColor || '#ffeb3b',
                    }
                  });
                  hideTipAndSelection();
                }}
              />
            )}
            highlightTransform={(
              highlight,
              index,
              setTip,
              hideTip,
              viewportToScaled,
              screenshot,
              isScrolledTo
            ) => {
              const isTextHighlight = !highlight.content?.image;

              const component = isTextHighlight ? (
                <Highlight
                  isScrolledTo={isScrolledTo}
                  position={highlight.position}
                  comment={highlight.comment}
                />
              ) : (
                <AreaHighlight
                  isScrolledTo={isScrolledTo}
                  highlight={highlight}
                  onChange={(boundingRect) => {
                    handleUpdateHighlight(
                      highlight.id,
                      { boundingRect: viewportToScaled(boundingRect) },
                      { image: screenshot(boundingRect) }
                    );
                  }}
                />
              );

              return (
                <Popup
                  popupContent={<HighlightPopup comment={highlight.comment} />}
                  onMouseOver={(popupContent) =>
                    setTip(highlight, (highlight) => popupContent)
                  }
                  onMouseOut={hideTip}
                  key={index}
                  children={component}
                />
              );
            }}
            highlights={highlights}
          />
        )}
      </PdfLoader>
    </div>
  );
};

export default PDFHighlighter;