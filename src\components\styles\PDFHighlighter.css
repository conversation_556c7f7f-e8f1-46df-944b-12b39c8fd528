/* PDF Highlighter Container */
.pdf-highlighter-container {
  width: 100%;
  height: 100%;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  flex: 1;
}

/* Loading and Error States */
.pdf-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #666;
  background-color: #f8f9fa;
}

.pdf-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #dc3545;
  text-align: center;
  background-color: #f8f9fa;
}

/* Highlight Popup Styles */
.highlight-popup {
  background: white;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 12px;
  max-width: 200px;
  border: 1px solid #e0e0e0;
}

.highlight-comment {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  line-height: 1.4;
}

.comment-emoji {
  font-size: 16px;
  flex-shrink: 0;
}

.comment-text {
  color: #333;
  word-break: break-word;
}

/* Text Size Responsive Scaling */
.pdf-highlighter-container.small {
  font-size: 0.9em;
}

.pdf-highlighter-container.large {
  font-size: 1.1em;
}

/* Override react-pdf-highlighter styles for better integration */
.PdfHighlighter {
  height: 100% !important;
}

.PdfHighlighter__container {
  background-color: #f8f9fa !important;
}

/* Tip (selection tooltip) styling */
.Tip {
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: 12px !important;
}

.Tip__card {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.Tip__input {
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  padding: 8px !important;
  font-size: 14px !important;
  margin-bottom: 8px !important;
  width: 200px !important;
}

.Tip__input:focus {
  outline: none !important;
  border-color: #4a90e2 !important;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2) !important;
}

.Tip__button {
  background-color: #4a90e2 !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 6px 12px !important;
  font-size: 14px !important;
  cursor: pointer !important;
  transition: background-color 0.2s !important;
}

.Tip__button:hover {
  background-color: #357abd !important;
}

/* Highlight styles with color support */
.Highlight {
  transition: opacity 0.2s ease !important;
}

.Highlight:hover {
  opacity: 0.8 !important;
}

.Highlight__part {
  border-radius: 2px !important;
  transition: all 0.2s ease !important;
}

/* Area highlight styles */
.AreaHighlight {
  border: 2px solid #4a90e2 !important;
  border-radius: 4px !important;
  background-color: rgba(74, 144, 226, 0.1) !important;
  transition: all 0.2s ease !important;
}

.AreaHighlight:hover {
  border-color: #357abd !important;
  background-color: rgba(74, 144, 226, 0.2) !important;
}

/* Popup styles */
.Popup {
  z-index: 10 !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .pdf-highlighter-container {
    padding: 10px;
  }

  .highlight-popup {
    max-width: 150px;
    padding: 6px 8px;
  }

  .highlight-comment {
    font-size: 12px;
  }

  .Tip__input {
    width: 150px !important;
    font-size: 12px !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .highlight-popup {
    border-width: 2px;
    border-color: #000;
  }

  .Highlight__part {
    border: 1px solid !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  .Highlight,
  .Highlight__part,
  .AreaHighlight,
  .Tip__button {
    transition: none !important;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .pdf-highlighter-container {
    background-color: #1a1a1a;
  }

  .pdf-loading,
  .pdf-error {
    background-color: #1a1a1a;
    color: #e0e0e0;
  }

  .highlight-popup {
    background: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
  }

  .Tip {
    background: #2d2d2d !important;
    border-color: #444 !important;
    color: #e0e0e0 !important;
  }

  .Tip__input {
    background: #1a1a1a !important;
    border-color: #444 !important;
    color: #e0e0e0 !important;
  }
}